generator client {
  provider      = "prisma-client-js"
  output        = "../node_modules/.prisma/client"
  binaryTargets = ["native", "rhel-openssl-3.0.x"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id            String          @id(map: "User_new_pkey") @default(cuid())
  name          String?
  email         String?         @unique
  emailVerified DateTime?
  image         String?
  isPremium     Boolean         @default(false)
  createdAt     DateTime        @default(now())
  updatedAt     DateTime        @updatedAt
  clerkId       String?         @unique
  SEOToolResult SEOToolResult[]
}

model ServiceRequest {
  id                     String               @id @default(cuid())
  createdAt              DateTime             @default(now())
  updatedAt              DateTime             @updatedAt
  question               String
  websiteType            String
  reference              String?
  name                   String
  phoneNumber            String
  email                  String
  brandName              String
  brandAge               String
  brandDescription       String
  hasDomain              Boolean
  domainName             String?
  websitesUnderDomain    String?
  hasHosting             Boolean
  needsLogo              Boolean
  categoryPages          String
  needsContent           Boolean
  providingProductImages Boolean
  additionalRequirements String?
  budget                 String
  status                 ServiceRequestStatus @default(PENDING)

  @@index([email])
  @@index([status])
  @@index([createdAt])
}

model Account {
  id                String  @id
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?

  @@unique([provider, providerAccountId])
}

model ContactMessage {
  id        Int      @id @default(autoincrement())
  fullName  String
  email     String
  message   String
  createdAt DateTime @default(now())
}

model ContactRequest {
  id        String   @id
  userId    String
  name      String
  email     String
  phone     String?
  service   String
  message   String
  status    String   @default("PENDING")
  createdAt DateTime @default(now())
  updatedAt DateTime

  @@index([userId])
}

model Domain {
  id         Int      @id @default(autoincrement())
  domainName String   @unique
  ipAddress  String?
  pdfData    Bytes?
  pdfPath    String?
  createdAt  DateTime @default(now())
  updatedAt  DateTime
  userId     String?
}

model KeywordSearch {
  id         Int      @id @default(autoincrement())
  keyword    String
  platform   String
  searchType String
  country    String
  language   String
  createdAt  DateTime @default(now())
  results    Json
  userId     String?
}

model SEOToolResult {
  id        String      @id
  userId    String
  toolType  SEOToolType
  url       String
  result    Json
  createdAt DateTime    @default(now())
  updatedAt DateTime
  User      User        @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([toolType])
  @@index([userId])
}

model Session {
  id           String   @id
  sessionToken String   @unique
  userId       String
  expires      DateTime
}

model UserUsage {
  id               Int      @id @default(autoincrement())
  userId           String   @unique
  freeReportsUsed  Int      @default(0)
  freeSearchesUsed Int      @default(0)
  lastResetDate    DateTime @default(now())
  createdAt        DateTime @default(now())
  updatedAt        DateTime
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

enum UserRole {
  FREELANCER
  CLIENT
}

enum ProjectStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  CANCELLED
}

enum PaymentStatus {
  PENDING
  COMPLETED
  FAILED
  REFUNDED
}

enum ServiceRequestStatus {
  PENDING
  CONTACTED
  IN_PROGRESS
  COMPLETED
  CANCELLED
}

enum SEOToolType {
  META_TAGS
  KEYWORD_DENSITY
  HEADER_TAGS
  INTERNAL_LINKS
  ALT_TEXT
  BROKEN_LINKS
  ROBOTS_TXT
  SITEMAP
  SEO_SCORE
  READABILITY
  TITLE_PREVIEW
  DESCRIPTION_PREVIEW
  STRUCTURED_DATA
  CANONICAL_URL
  MOBILE_FRIENDLY
}
