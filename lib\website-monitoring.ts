import { PrismaClient } from '@prisma/client';
import { prisma } from './db';

export interface WebsiteInput {
  url: string;
  userId: string;
}

export interface ScanResult {
  websiteId: string;
  pageUrl: string;
  accessibility?: {
    wcagLevel: string;
    totalIssues: number;
    criticalIssues: number;
    warningIssues: number;
    issueDetails: any;
  };
  performance?: {
    loadTime: number;
    firstPaint: number;
    firstContentfulPaint: number;
    speedIndex: number;
    performanceScore: number;
    metrics: any;
  };
  seo?: {
    metaTags: any;
    headings: any;
    images: any;
    links: any;
    seoScore: number;
    issues: any;
  };
  contentQuality?: {
    readabilityScore: number;
    spellingIssues: number;
    grammarIssues: number;
    brokenLinks: number;
    duplicateContent: boolean;
    qualityScore: number;
    details: any;
  };
}

export async function addWebsite(data: WebsiteInput) {
  return prisma.website.create({
    data: {
      url: data.url,
      userId: data.userId,
    },
  });
}

export async function startWebsiteScan(websiteId: string) {
  return prisma.websiteScan.create({
    data: {
      websiteId,
      status: 'running',
    },
  });
}

export async function updateScanResults(scanId: string, results: ScanResult) {
  const {
    websiteId,
    pageUrl,
    accessibility,
    performance,
    seo,
    contentQuality,
  } = results;

  // Create all results in a transaction
  return prisma.$transaction(async (tx) => {
    if (accessibility) {
      await tx.accessibilityResult.create({
        data: {
          websiteId,
          pageUrl,
          ...accessibility,
        },
      });
    }

    if (performance) {
      await tx.performanceResult.create({
        data: {
          websiteId,
          pageUrl,
          ...performance,
        },
      });
    }

    if (seo) {
      await tx.sEOResult.create({
        data: {
          websiteId,
          pageUrl,
          ...seo,
        },
      });
    }

    if (contentQuality) {
      await tx.contentQualityResult.create({
        data: {
          websiteId,
          pageUrl,
          ...contentQuality,
        },
      });
    }

    // Update scan status
    await tx.websiteScan.update({
      where: { id: scanId },
      data: {
        scannedPages: { increment: 1 },
        status: 'completed',
      },
    });
  });
}

export async function getWebsiteResults(websiteId: string) {
  return prisma.$transaction([
    prisma.accessibilityResult.findMany({
      where: { websiteId },
      orderBy: { scanDate: 'desc' },
      take: 10,
    }),
    prisma.performanceResult.findMany({
      where: { websiteId },
      orderBy: { scanDate: 'desc' },
      take: 10,
    }),
    prisma.sEOResult.findMany({
      where: { websiteId },
      orderBy: { scanDate: 'desc' },
      take: 10,
    }),
    prisma.contentQualityResult.findMany({
      where: { websiteId },
      orderBy: { scanDate: 'desc' },
      take: 10,
    }),
  ]);
}
